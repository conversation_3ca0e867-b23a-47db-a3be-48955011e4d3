#!/bin/bash

# Rishi Core - Setup Script
# This script sets up the development environment for the Rishi Core project

set -e  # Exit on any error

# Database setup operation
echo "Database setup"
echo


CONTAINER_NAME="rishiflow-db"
HOST_DATA_DIR="$HOME/.rishiflow/db/postgresql"
HOST_PORT=5433

if [ -x "$(command -v podman)" ]; then

    if podman container exists "$CONTAINER_NAME"; then
        echo "Removing existing container '$CONTAINER_NAME'..."
        podman rm -f "$CONTAINER_NAME"
    fi

    
        # Prompt for password
        if [ -z "$POSTGRES_PASSWORD" ]; then
            read -sp "Enter a password to be used for your PostgreSQL database: " password
            echo
        else
            password="$POSTGRES_PASSWORD"
        fi

        

        # Run new container
        podman run -dt --name "$CONTAINER_NAME" \
            -e POSTGRES_PASSWORD="$password" \
            -p "$HOST_PORT:5432" \
            docker.io/library/postgres:17
        echo "Container '$CONTAINER_NAME' has been created and started."

        # Wait for the database to be ready using pg_isready
        echo "Waiting for database to be ready..."
        retries=15
        count=0
        until podman exec "$CONTAINER_NAME" pg_isready -U postgres -q; do
            count=$((count+1))
            if [ $count -ge $retries ]; then
                echo "❌ Database did not become ready in time. Check container logs:"
                podman logs "$CONTAINER_NAME"
                exit 1
            fi
            echo "Still waiting... (attempt $count/$retries)"
            sleep 2
        done
        echo "✅ Database is ready to accept connections."

        # Create the database
        echo "Creating database 'rishi_db'..."
        podman exec "$CONTAINER_NAME" psql -U postgres -c "CREATE DATABASE rishi_db;"
        echo "Database 'rishi_db' has been created."

        # Create users and grant privileges
        echo "Creating database users..."
        podman cp ./script/create_users.sql "$CONTAINER_NAME":/create_users.sql

        read -sp "Enter a password for the application user (app_user): " APP_USER_PASSWORD
        echo
        read -sp "Enter a password for the migration user (migration_user): " MIGRATION_USER_PASSWORD
        echo

        podman exec "$CONTAINER_NAME" psql -U postgres -d rishi_db \
            -v APP_USER_PASSWORD="'$APP_USER_PASSWORD'" \
            -v MIGRATION_USER_PASSWORD="'$MIGRATION_USER_PASSWORD'" \
            -f /create_users.sql
        echo "Database users have been created."
fi



# Change to project root directory (parent of script directory)
cd "$(dirname "$0")/.."

echo "🚀 Setting up Rishi Core development environment..."

# Node.js version to install
NODE_VERSION="22"
REQUIRED_VERSION="22.0.0"

# Check if nvm is installed
if ! command -v nvm &> /dev/null; then
    echo "📦 nvm not found. Installing nvm..."

    # Download and install nvm
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

    # Source nvm script to make it available in current session
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

    if ! command -v nvm &> /dev/null; then
        echo "❌ Failed to install nvm. Please install it manually:"
        echo "   Visit: https://github.com/nvm-sh/nvm#installing-and-updating"
        exit 1
    fi

    echo "✅ nvm installed successfully"
else
    echo "✅ nvm is already installed"

    # Source nvm to ensure it's available
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
fi

# Install and use Node.js 22
echo "📦 Installing Node.js $NODE_VERSION (latest)..."
nvm install $NODE_VERSION
nvm use $NODE_VERSION

# Verify Node.js installation
if ! command -v node &> /dev/null; then
    echo "❌ Node.js installation failed"
    exit 1
fi

# Check Node.js version
CURRENT_NODE_VERSION=$(node --version | cut -d'v' -f2)
echo "✅ Node.js v$CURRENT_NODE_VERSION is now active"

# Verify we have the correct major version
MAJOR_VERSION=$(echo $CURRENT_NODE_VERSION | cut -d'.' -f1)
if [ "$MAJOR_VERSION" != "$NODE_VERSION" ]; then
    echo "⚠️  Expected Node.js $NODE_VERSION but got $MAJOR_VERSION"
    echo "   Attempting to switch to Node.js $NODE_VERSION..."
    nvm use $NODE_VERSION
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not available. This should come with Node.js."
    exit 1
fi

echo "✅ Node.js $(node --version) and npm $(npm --version) are available"

# Create .nvmrc file to lock Node.js version for the project
echo "📝 Creating .nvmrc file to lock Node.js version..."
echo "$NODE_VERSION" > .nvmrc
echo "✅ Created .nvmrc with Node.js $NODE_VERSION"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Verify TypeScript installation
if ! npx tsc --version &> /dev/null; then
    echo "❌ TypeScript installation failed"
    exit 1
fi

echo "✅ TypeScript $(npx tsc --version) is available"

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p dist
mkdir -p logs

# Build the project to verify everything works
echo "🔨 Building project to verify setup..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Node.js Environment:"
echo "  Node.js: $(node --version)"
echo "  npm: $(npm --version)"
echo "  nvm: $(nvm --version)"
echo ""
echo "💡 To use this Node.js version in new terminal sessions:"
echo "  nvm use 22"
echo "  # or simply: nvm use (reads from .nvmrc)"
echo ""
echo "Available commands:"
echo "  npm run dev     - Run in development mode with hot reload"
echo "  npm run build   - Build the project for production"
echo "  npm run start   - Run the built project"
echo "  npm run lint    - Check code quality with ESLint"
echo "  npm run fix     - Fix linting and formatting issues"
echo ""
echo "You can also use the convenience script:"
echo "  script/run.sh        - Lint, build and run the project"
echo "  script/run.sh --lint-fix - Fix code quality issues"
echo ""
echo "Happy coding! 🚀"
