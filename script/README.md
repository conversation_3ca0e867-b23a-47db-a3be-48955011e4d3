# Rishi Core Database User Roles

This document outlines the roles and responsibilities of the database users created by the `setup.sh` script.

## Users

### `app_user`

- **Purpose**: This user is intended for the application to interact with the database.
- **Privileges**:
    - `SELECT`: Can read data from tables.
    - `INSERT`: Can add new data to tables.
    - `UPDATE`: Can modify existing data in tables.
    - `DELETE`: Can remove data from tables.
- **Usage**: The application should use this user's credentials to connect to the `rishi_db` database.

### `migration_user`

- **Purpose**: This user is intended for running database migrations.
- **Privileges**:
    - `CREATE`: Can create new tables.
    - `ALTER`: Can modify the structure of existing tables.
    - `DROP`: Can delete tables.
- **Usage**: This user should be used by the migration tool to apply schema changes to the `rishi_db` database.
